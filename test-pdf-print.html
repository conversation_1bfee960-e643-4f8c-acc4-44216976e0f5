<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Print Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: #f0f0f0;
            padding: 15px 20px;
            border-bottom: 1px solid #ccc;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        
        .print-button, .share-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-left: 10px;
        }

        .print-button:hover, .share-button:hover {
            background-color: #0056b3;
        }

        .print-button:active, .share-button:active {
            background-color: #004085;
        }

        .share-button {
            background-color: #28a745;
        }

        .share-button:hover {
            background-color: #218838;
        }

        .share-button:active {
            background-color: #1e7e34;
        }

        .button-group {
            display: flex;
            gap: 10px;
        }
        
        .iframe-container {
            flex: 1;
            padding: 0;
            margin: 0;
        }
        
        #pdf-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        /* Print styles */
        @media print {
            .header {
                display: none;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
            
            .iframe-container {
                height: 100vh;
            }
            
            #pdf-iframe {
                height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>PDF Print Test</h1>
        <div class="button-group">
            <button class="print-button" onclick="printPDF()">🖨️ Print PDF</button>
            <button class="share-button" onclick="sharePDF()">📤 Share PDF</button>
        </div>
    </div>
    
    <div class="iframe-container">
        <iframe id="pdf-iframe" src="data/pu.pdf" title="PDF Document">
            <p>Your browser does not support iframes. Please <a href="data/pu.pdf">download the PDF</a> to view it.</p>
        </iframe>
    </div>

    <script>
        function printPDF() {
            try {
                // Method 1: Try to print the iframe content directly
                const iframe = document.getElementById('pdf-iframe');

                // Check if iframe is loaded and accessible
                if (iframe.contentWindow) {
                    iframe.contentWindow.focus();
                    iframe.contentWindow.print();
                } else {
                    // Method 2: Fallback - print the entire page (iframe will be visible due to print styles)
                    window.print();
                }
            } catch (error) {
                console.log('Direct iframe printing failed, using fallback method');
                // Method 3: Final fallback - print the whole page
                window.print();
            }
        }

        async function sharePDF() {
            try {
                // Check if Navigator.share is supported
                if (!navigator.share) {
                    // Fallback for browsers that don't support Navigator.share
                    const pdfUrl = window.location.origin + '/data/pu.pdf';

                    // Try to copy URL to clipboard
                    if (navigator.clipboard) {
                        await navigator.clipboard.writeText(pdfUrl);
                        alert('PDF URL copied to clipboard: ' + pdfUrl);
                    } else {
                        // Final fallback - show URL in alert
                        alert('Share not supported. PDF URL: ' + pdfUrl);
                    }
                    return;
                }

                // Fetch the PDF file as a blob
                const pdfUrl = 'data/pu.pdf';
                const response = await fetch(pdfUrl);

                if (!response.ok) {
                    throw new Error('Failed to fetch PDF file');
                }

                const blob = await response.blob();
                const file = new File([blob], 'document.pdf', { type: 'application/pdf' });

                // Use Navigator.share to share the PDF file
                await navigator.share({
                    title: 'PDF Document',
                    text: 'Check out this PDF document',
                    files: [file]
                });

                console.log('PDF shared successfully');

            } catch (error) {
                console.error('Error sharing PDF:', error);

                // Fallback: copy URL to clipboard or show alert
                try {
                    const pdfUrl = window.location.origin + '/data/pu.pdf';
                    if (navigator.clipboard) {
                        await navigator.clipboard.writeText(pdfUrl);
                        alert('Could not share file directly. PDF URL copied to clipboard: ' + pdfUrl);
                    } else {
                        alert('Could not share file. PDF URL: ' + pdfUrl);
                    }
                } catch (clipboardError) {
                    alert('Could not share file or copy URL. Please manually share: ' + window.location.origin + '/data/pu.pdf');
                }
            }
        }
        
        // Alternative method: Open PDF in new window and print
        function printPDFNewWindow() {
            const pdfUrl = 'data/pu.pdf';
            const printWindow = window.open(pdfUrl, '_blank');
            
            printWindow.onload = function() {
                printWindow.print();
                // Optionally close the window after printing
                // printWindow.onafterprint = function() {
                //     printWindow.close();
                // };
            };
        }
        
        // You can uncomment the line below to use the new window method instead
        // document.querySelector('.print-button').onclick = printPDFNewWindow;
        
        // Handle iframe load events
        document.getElementById('pdf-iframe').onload = function() {
            console.log('PDF iframe loaded successfully');
        };
        
        document.getElementById('pdf-iframe').onerror = function() {
            console.log('Error loading PDF iframe');
        };
    </script>
</body>
</html>
