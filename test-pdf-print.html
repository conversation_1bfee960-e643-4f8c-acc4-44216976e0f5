<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Print Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: #f0f0f0;
            padding: 15px 20px;
            border-bottom: 1px solid #ccc;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        
        .print-button, .share-button, .share-url-button, .multi-print-button, .pdfjs-print-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-left: 10px;
        }

        .print-button:hover, .share-button:hover, .share-url-button:hover, .multi-print-button:hover, .pdfjs-print-button:hover {
            background-color: #0056b3;
        }

        .print-button:active, .share-button:active, .share-url-button:active, .multi-print-button:active, .pdfjs-print-button:active {
            background-color: #004085;
        }

        .share-button {
            background-color: #28a745;
        }

        .share-button:hover {
            background-color: #218838;
        }

        .share-button:active {
            background-color: #1e7e34;
        }

        .share-url-button {
            background-color: #17a2b8;
        }

        .share-url-button:hover {
            background-color: #138496;
        }

        .share-url-button:active {
            background-color: #117a8b;
        }

        .multi-print-button {
            background-color: #6f42c1;
        }

        .multi-print-button:hover {
            background-color: #5a32a3;
        }

        .multi-print-button:active {
            background-color: #4c2a85;
        }

        .pdfjs-print-button {
            background-color: #fd7e14;
        }

        .pdfjs-print-button:hover {
            background-color: #e8681a;
        }

        .pdfjs-print-button:active {
            background-color: #d35400;
        }

        .button-group {
            display: flex;
            gap: 10px;
        }

        #pdfjs-canvas {
            display: none;
            border: 1px solid #ccc;
            margin: 10px auto;
            max-width: 100%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        #pdfjs-canvas.visible {
            display: block;
        }

        .pdfjs-status {
            text-align: center;
            margin: 10px;
            font-style: italic;
            color: #666;
        }
        
        .pdf-container {
            flex: 1;
            padding: 0;
            margin: 0;
        }

        #pdf-object {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        /* Print styles */
        @media print {
            .header {
                display: none;
            }
            
            body {
                margin: 0;
                padding: 0;
            }

            .pdf-container {
                height: 100vh;
            }

            #pdf-object {
                height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>PDF Print Test</h1>
        <div class="button-group">
            <button class="print-button" onclick="printPDF()">🖨️ Print PDF</button>
            <button class="multi-print-button" onclick="multiPrint()">🔄 Multi Print</button>
            <button class="pdfjs-print-button" onclick="printWithPDFJS()">📄 PDF.js Print</button>
            <button class="share-button" onclick="sharePDF()">📤 Share PDF</button>
            <button class="share-url-button" onclick="shareURL()">🔗 Share URL</button>
        </div>
    </div>
    
    <div class="pdf-container">
        <object id="pdf-object" data="" type="application/pdf" title="PDF Document">
            <p>Your browser does not support PDF viewing. Please <a href="" id="pdf-download-link">download the PDF</a> to view it.</p>
        </object>
    </div>

    <!-- PDF.js Canvas Container -->
    <div id="pdfjs-status" class="pdfjs-status"></div>
    <canvas id="pdfjs-canvas"></canvas>

    <!-- PDF.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.6.82/pdf.min.mjs" type="module"></script>

    <script>
        // PDF file configuration - change this constant to use a different PDF
        const PDF_FILE_PATH = 'data/veolia-protocol.pdf';

        // PDF.js configuration
        let pdfDoc = null;
        let currentPage = 1;
        let totalPages = 0;

        // Set the PDF source when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            const pdfObject = document.getElementById('pdf-object');
            const downloadLink = document.getElementById('pdf-download-link');

            pdfObject.data = PDF_FILE_PATH;
            downloadLink.href = PDF_FILE_PATH;
        });

        function printPDF() {
            console.log('=== Starting PDF Print Process ===');

            // Detect mobile browsers
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isAndroid = /Android/.test(navigator.userAgent);

            console.log('Device detection:', { isMobile, isIOS, isAndroid });

            try {
                // Method 1: For mobile devices, use iframe approach (most reliable)
                if (isMobile) {
                    console.log('Mobile device detected, using iframe method');
                    printPDFViaIframe();
                    return;
                }

                // Method 2: Try to print the object content directly (desktop)
                const pdfObject = document.getElementById('pdf-object');

                // Check if object has contentWindow (PDF plugin loaded)
                if (pdfObject.contentWindow) {
                    try {
                        pdfObject.contentWindow.focus();
                        pdfObject.contentWindow.print();
                        console.log('PDF printed via object contentWindow');
                        return;
                    } catch (e) {
                        console.log('contentWindow.print() failed:', e);
                    }
                }

                // Method 3: Try to access the PDF document directly
                if (pdfObject.contentDocument) {
                    try {
                        pdfObject.contentDocument.defaultView.print();
                        console.log('PDF printed via object contentDocument');
                        return;
                    } catch (e) {
                        console.log('contentDocument.print() failed:', e);
                    }
                }

                // Method 4: Fallback to iframe method for desktop too
                console.log('Object printing methods failed, using iframe fallback');
                printPDFViaIframe();

            } catch (error) {
                console.log('All object printing methods failed:', error);
                // Final fallback - iframe method
                printPDFViaIframe();
            }
        }

        function printPDFViaIframe() {
            console.log('Using iframe printing method');

            try {
                // Create a hidden iframe
                const iframe = document.createElement('iframe');
                iframe.style.position = 'fixed';
                iframe.style.top = '-1000px';
                iframe.style.left = '-1000px';
                iframe.style.width = '1px';
                iframe.style.height = '1px';
                iframe.style.opacity = '0';
                iframe.style.border = 'none';

                // Set the PDF source
                iframe.src = PDF_FILE_PATH;

                // Handle iframe load
                iframe.onload = function() {
                    console.log('Iframe loaded, attempting to print');

                    setTimeout(() => {
                        try {
                            // Try to print the iframe content
                            if (iframe.contentWindow) {
                                iframe.contentWindow.focus();
                                iframe.contentWindow.print();
                                console.log('PDF printed via iframe');
                            } else {
                                throw new Error('No contentWindow access');
                            }
                        } catch (e) {
                            console.log('Iframe printing failed:', e);
                            // Final fallback - open in new window
                            openPDFInNewWindow();
                        }

                        // Clean up iframe after a delay
                        setTimeout(() => {
                            if (iframe.parentNode) {
                                iframe.parentNode.removeChild(iframe);
                            }
                        }, 1000);
                    }, 500); // Small delay to ensure PDF is loaded
                };

                // Handle iframe error
                iframe.onerror = function() {
                    console.log('Iframe failed to load PDF');
                    openPDFInNewWindow();
                    if (iframe.parentNode) {
                        iframe.parentNode.removeChild(iframe);
                    }
                };

                // Add iframe to document
                document.body.appendChild(iframe);

            } catch (error) {
                console.log('Iframe creation failed:', error);
                openPDFInNewWindow();
            }
        }

        function openPDFInNewWindow() {
            console.log('Opening PDF in new window for printing');

            try {
                const printWindow = window.open(PDF_FILE_PATH, '_blank', 'width=800,height=600');

                if (printWindow) {
                    printWindow.onload = function() {
                        // Small delay to ensure PDF is rendered
                        setTimeout(() => {
                            try {
                                printWindow.focus();
                                printWindow.print();
                                console.log('PDF printed via new window');

                                // Optional: Close window after printing (uncomment if desired)
                                // printWindow.onafterprint = function() {
                                //     printWindow.close();
                                // };
                            } catch (e) {
                                console.log('New window printing failed:', e);
                                alert('Please use your browser\'s print function (Ctrl+P or Cmd+P) to print the PDF.');
                            }
                        }, 1000);
                    };
                } else {
                    throw new Error('Popup blocked or failed to open');
                }
            } catch (error) {
                console.log('New window method failed:', error);
                // Final fallback - show instructions
                alert('Unable to print automatically. Please:\n1. Right-click the PDF\n2. Select "Print" from the context menu\nOr use Ctrl+P (Cmd+P on Mac)');
            }
        }

        async function sharePDF() {
            try {
                // Construct the full PDF URL
                const baseUrl = window.location.href.replace(/\/[^\/]*$/, '');
                const pdfUrl = baseUrl + '/' + PDF_FILE_PATH;

                console.log('Attempting to share PDF from:', pdfUrl);

                // Check if Navigator.share is supported
                if (!navigator.share) {
                    console.log('Navigator.share not supported, using fallback');
                    await fallbackShare(pdfUrl);
                    return;
                }

                // First try sharing just the URL (more reliable on iOS)
                try {
                    await navigator.share({
                        title: 'PDF Document',
                        text: 'Check out this PDF document',
                        url: pdfUrl
                    });
                    console.log('PDF URL shared successfully');
                    return;
                } catch (urlShareError) {
                    console.log('URL sharing failed, trying file sharing:', urlShareError);
                }

                // If URL sharing fails, try file sharing
                try {
                    // Check if file sharing is supported
                    if (!navigator.canShare || !navigator.canShare({ files: [new File([''], 'test.pdf', { type: 'application/pdf' })] })) {
                        throw new Error('File sharing not supported');
                    }

                    // Fetch the PDF file as a blob
                    const response = await fetch(pdfUrl);

                    if (!response.ok) {
                        throw new Error(`Failed to fetch PDF file: ${response.status} ${response.statusText}`);
                    }

                    const blob = await response.blob();
                    // Extract filename from path or use default
                    const filename = PDF_FILE_PATH.split('/').pop() || 'document.pdf';
                    const file = new File([blob], filename, { type: 'application/pdf' });

                    // Use Navigator.share to share the PDF file
                    await navigator.share({
                        title: 'PDF Document',
                        text: 'Check out this PDF document',
                        files: [file]
                    });

                    console.log('PDF file shared successfully');

                } catch (fileShareError) {
                    console.log('File sharing failed:', fileShareError);
                    throw fileShareError;
                }

            } catch (error) {
                console.error('Error sharing PDF:', error);

                // Fallback to URL sharing methods
                const baseUrl = window.location.href.replace(/\/[^\/]*$/, '');
                const pdfUrl = baseUrl + '/' + PDF_FILE_PATH;
                await fallbackShare(pdfUrl);
            }
        }

        async function shareURL() {
            try {
                // Construct the full PDF URL
                const baseUrl = window.location.href.replace(/\/[^\/]*$/, '');
                const pdfUrl = baseUrl + '/' + PDF_FILE_PATH;

                console.log('Attempting to share PDF URL:', pdfUrl);

                // Check if Navigator.share is supported
                if (navigator.share) {
                    try {
                        await navigator.share({
                            title: 'PDF Document',
                            text: 'Check out this PDF document',
                            url: pdfUrl
                        });
                        console.log('PDF URL shared successfully via Navigator.share');
                        return;
                    } catch (shareError) {
                        console.log('Navigator.share failed:', shareError);
                        // Fall through to clipboard fallback
                    }
                }

                // Fallback to clipboard
                await fallbackShare(pdfUrl);

            } catch (error) {
                console.error('Error sharing PDF URL:', error);

                // Final fallback
                const baseUrl = window.location.href.replace(/\/[^\/]*$/, '');
                const pdfUrl = baseUrl + '/' + PDF_FILE_PATH;
                alert('Please copy this PDF URL to share:\n' + pdfUrl);
            }
        }

        async function fallbackShare(pdfUrl) {
            try {
                // Try to copy URL to clipboard
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    await navigator.clipboard.writeText(pdfUrl);
                    alert('PDF URL copied to clipboard:\n' + pdfUrl);
                } else {
                    // Final fallback - show URL in alert for manual copying
                    alert('Please copy this PDF URL to share:\n' + pdfUrl);
                }
            } catch (clipboardError) {
                console.error('Clipboard error:', clipboardError);
                // Final fallback - show URL in alert
                alert('Please copy this PDF URL to share:\n' + pdfUrl);
            }
        }

        async function multiPrint() {
            console.log('=== Starting Multi-Method PDF Print Process ===');

            // Device and browser detection
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isAndroid = /Android/.test(navigator.userAgent);
            const isPWA = window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone;

            console.log('Device info:', { isMobile, isIOS, isAndroid, isPWA });

            // Show user what methods will be attempted
            const methods = [];
            if (!isMobile) {
                methods.push('Object contentWindow print');
                methods.push('Object contentDocument print');
            }
            methods.push('Hidden iframe print');
            methods.push('New window print');
            methods.push('Page print fallback');

            const userConfirm = confirm(
                `Multi-Print will attempt ${methods.length} different printing methods:\n\n` +
                methods.map((method, index) => `${index + 1}. ${method}`).join('\n') +
                '\n\nThis may open multiple print dialogs. Continue?'
            );

            if (!userConfirm) {
                console.log('User cancelled multi-print');
                return;
            }

            let successCount = 0;
            let currentMethod = 1;

            // Method 1: Object contentWindow (Desktop only)
            if (!isMobile) {
                try {
                    console.log(`Method ${currentMethod}: Object contentWindow print`);
                    const pdfObject = document.getElementById('pdf-object');

                    if (pdfObject.contentWindow) {
                        await new Promise((resolve) => {
                            setTimeout(() => {
                                try {
                                    pdfObject.contentWindow.focus();
                                    pdfObject.contentWindow.print();
                                    console.log('✅ Object contentWindow print succeeded');
                                    successCount++;
                                } catch (e) {
                                    console.log('❌ Object contentWindow print failed:', e);
                                }
                                resolve();
                            }, 1000);
                        });
                    } else {
                        console.log('❌ Object contentWindow not available');
                    }
                } catch (error) {
                    console.log('❌ Object contentWindow method failed:', error);
                }
                currentMethod++;

                // Method 2: Object contentDocument (Desktop only)
                try {
                    console.log(`Method ${currentMethod}: Object contentDocument print`);
                    const pdfObject = document.getElementById('pdf-object');

                    if (pdfObject.contentDocument) {
                        await new Promise((resolve) => {
                            setTimeout(() => {
                                try {
                                    pdfObject.contentDocument.defaultView.print();
                                    console.log('✅ Object contentDocument print succeeded');
                                    successCount++;
                                } catch (e) {
                                    console.log('❌ Object contentDocument print failed:', e);
                                }
                                resolve();
                            }, 1000);
                        });
                    } else {
                        console.log('❌ Object contentDocument not available');
                    }
                } catch (error) {
                    console.log('❌ Object contentDocument method failed:', error);
                }
                currentMethod++;
            }

            // Method 3: Hidden iframe
            try {
                console.log(`Method ${currentMethod}: Hidden iframe print`);
                await multiPrintViaIframe();
                successCount++;
                console.log('✅ Hidden iframe print completed');
            } catch (error) {
                console.log('❌ Hidden iframe print failed:', error);
            }
            currentMethod++;

            // Wait before next method
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Method 4: New window
            try {
                console.log(`Method ${currentMethod}: New window print`);
                await multiPrintViaNewWindow();
                successCount++;
                console.log('✅ New window print completed');
            } catch (error) {
                console.log('❌ New window print failed:', error);
            }
            currentMethod++;

            // Wait before next method
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Method 5: Page print (fallback)
            try {
                console.log(`Method ${currentMethod}: Page print fallback`);
                await new Promise((resolve) => {
                    setTimeout(() => {
                        try {
                            window.print();
                            console.log('✅ Page print succeeded');
                            successCount++;
                        } catch (e) {
                            console.log('❌ Page print failed:', e);
                        }
                        resolve();
                    }, 1000);
                });
            } catch (error) {
                console.log('❌ Page print method failed:', error);
            }

            // Summary
            console.log(`=== Multi-Print Complete ===`);
            console.log(`Successful methods: ${successCount}/${methods.length}`);

            alert(
                `Multi-Print completed!\n\n` +
                `Methods attempted: ${methods.length}\n` +
                `Successful: ${successCount}\n` +
                `Failed: ${methods.length - successCount}\n\n` +
                `Check the browser console for detailed logs.`
            );
        }

        async function multiPrintViaIframe() {
            return new Promise((resolve, reject) => {
                try {
                    const iframe = document.createElement('iframe');
                    iframe.style.position = 'fixed';
                    iframe.style.top = '-1000px';
                    iframe.style.left = '-1000px';
                    iframe.style.width = '1px';
                    iframe.style.height = '1px';
                    iframe.style.opacity = '0';
                    iframe.style.border = 'none';
                    iframe.src = PDF_FILE_PATH;

                    iframe.onload = function() {
                        setTimeout(() => {
                            try {
                                if (iframe.contentWindow) {
                                    iframe.contentWindow.focus();
                                    iframe.contentWindow.print();
                                    resolve();
                                } else {
                                    reject(new Error('No contentWindow access'));
                                }
                            } catch (e) {
                                reject(e);
                            }

                            setTimeout(() => {
                                if (iframe.parentNode) {
                                    iframe.parentNode.removeChild(iframe);
                                }
                            }, 1000);
                        }, 500);
                    };

                    iframe.onerror = function() {
                        reject(new Error('Iframe failed to load'));
                        if (iframe.parentNode) {
                            iframe.parentNode.removeChild(iframe);
                        }
                    };

                    document.body.appendChild(iframe);

                } catch (error) {
                    reject(error);
                }
            });
        }

        async function multiPrintViaNewWindow() {
            return new Promise((resolve, reject) => {
                try {
                    const printWindow = window.open(PDF_FILE_PATH, '_blank', 'width=800,height=600');

                    if (printWindow) {
                        printWindow.onload = function() {
                            setTimeout(() => {
                                try {
                                    printWindow.focus();
                                    printWindow.print();
                                    resolve();
                                } catch (e) {
                                    reject(e);
                                }
                            }, 1000);
                        };
                    } else {
                        reject(new Error('Popup blocked or failed to open'));
                    }
                } catch (error) {
                    reject(error);
                }
            });
        }

        async function printWithPDFJS() {
            console.log('=== Starting PDF.js Print Process ===');

            const statusElement = document.getElementById('pdfjs-status');
            const canvas = document.getElementById('pdfjs-canvas');

            try {
                statusElement.textContent = 'Loading PDF.js library...';

                // Import PDF.js dynamically
                const pdfjsLib = await import('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.6.82/pdf.min.mjs');

                // Set worker source
                pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.6.82/pdf.worker.min.mjs';

                statusElement.textContent = 'Loading PDF document...';
                console.log('Loading PDF:', PDF_FILE_PATH);

                // Load the PDF document
                const loadingTask = pdfjsLib.getDocument(PDF_FILE_PATH);
                pdfDoc = await loadingTask.promise;
                totalPages = pdfDoc.numPages;

                console.log(`PDF loaded successfully. Pages: ${totalPages}`);
                statusElement.textContent = `PDF loaded: ${totalPages} page(s). Rendering for print...`;

                // Render all pages for printing
                await renderAllPagesForPrint(pdfjsLib);

            } catch (error) {
                console.error('PDF.js print error:', error);
                statusElement.textContent = `Error: ${error.message}`;
                alert(`PDF.js printing failed: ${error.message}`);
            }
        }

        async function renderAllPagesForPrint(pdfjsLib) {
            const statusElement = document.getElementById('pdfjs-status');
            const canvas = document.getElementById('pdfjs-canvas');
            const ctx = canvas.getContext('2d');

            try {
                // Create a container for all pages
                const printContainer = document.createElement('div');
                printContainer.id = 'pdfjs-print-container';
                printContainer.style.display = 'none';
                printContainer.style.position = 'fixed';
                printContainer.style.top = '0';
                printContainer.style.left = '0';
                printContainer.style.zIndex = '9999';
                printContainer.style.backgroundColor = 'white';

                // Add print-specific styles
                const printStyle = document.createElement('style');
                printStyle.id = 'pdfjs-print-styles';
                printStyle.textContent = `
                    @media print {
                        /* Hide everything except our print container */
                        body * {
                            visibility: hidden !important;
                        }

                        /* Show only the PDF print container */
                        #pdfjs-print-container,
                        #pdfjs-print-container * {
                            visibility: visible !important;
                        }

                        /* Reset print container positioning */
                        #pdfjs-print-container {
                            position: static !important;
                            display: block !important;
                            width: 100% !important;
                            height: auto !important;
                            margin: 0 !important;
                            padding: 0 !important;
                            background: white !important;
                            z-index: auto !important;
                        }

                        /* Page break settings */
                        .pdfjs-print-page {
                            page-break-after: always;
                            page-break-inside: avoid;
                            margin: 0 !important;
                            padding: 0 !important;
                            width: 100% !important;
                            height: auto !important;
                            display: block !important;
                        }

                        /* Don't break after the last page */
                        .pdfjs-print-page:last-child {
                            page-break-after: avoid;
                        }

                        /* Hide page margins and headers/footers if possible */
                        @page {
                            margin: 0.5in;
                            size: auto;
                        }
                    }
                `;
                document.head.appendChild(printStyle);

                // Render each page
                for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
                    statusElement.textContent = `Rendering page ${pageNum} of ${totalPages}...`;

                    const page = await pdfDoc.getPage(pageNum);
                    const viewport = page.getViewport({ scale: 2.0 }); // Higher scale for better print quality

                    // Create canvas for this page
                    const pageCanvas = document.createElement('canvas');
                    pageCanvas.className = 'pdfjs-print-page';
                    pageCanvas.width = viewport.width;
                    pageCanvas.height = viewport.height;
                    pageCanvas.style.width = '100%';
                    pageCanvas.style.height = 'auto';
                    pageCanvas.style.maxWidth = '8.5in'; // Standard letter width

                    const pageCtx = pageCanvas.getContext('2d');

                    // Render page
                    await page.render({
                        canvasContext: pageCtx,
                        viewport: viewport
                    }).promise;

                    printContainer.appendChild(pageCanvas);

                    console.log(`Page ${pageNum} rendered successfully`);
                }

                // Add container to document
                document.body.appendChild(printContainer);

                // Show preview in main canvas (first page)
                if (totalPages > 0) {
                    const firstPage = await pdfDoc.getPage(1);
                    const viewport = firstPage.getViewport({ scale: 1.5 });

                    canvas.width = viewport.width;
                    canvas.height = viewport.height;
                    canvas.style.width = Math.min(viewport.width, 800) + 'px';
                    canvas.style.height = 'auto';

                    await firstPage.render({
                        canvasContext: ctx,
                        viewport: viewport
                    }).promise;

                    canvas.classList.add('visible');
                }

                statusElement.textContent = `Ready to print ${totalPages} page(s). Click OK to open print dialog.`;

                // Confirm and print
                const userConfirm = confirm(
                    `PDF.js has rendered ${totalPages} page(s) for high-quality printing.\n\n` +
                    `This will open the browser's print dialog with all pages ready.\n\n` +
                    `Continue with printing?`
                );

                if (userConfirm) {
                    console.log('Starting print process...');
                    statusElement.textContent = 'Opening print dialog...';

                    // Print in current window
                    try {
                        await printInCurrentWindow(printContainer);
                        statusElement.textContent = `Print dialog opened for ${totalPages} page(s).`;
                    } catch (currentWindowError) {
                        console.error('Print method failed:', currentWindowError);
                        statusElement.textContent = 'Print failed. Please try the regular Print PDF button.';
                        alert('PDF.js printing failed. Please try the regular "Print PDF" button instead.');
                    }
                } else {
                    statusElement.textContent = 'Print cancelled by user.';
                }

                // Cleanup after a delay
                setTimeout(() => {
                    if (printContainer.parentNode) {
                        printContainer.parentNode.removeChild(printContainer);
                    }
                    if (printStyle.parentNode) {
                        printStyle.parentNode.removeChild(printStyle);
                    }
                    canvas.classList.remove('visible');
                    statusElement.textContent = '';
                }, 10000);

            } catch (error) {
                console.error('Error rendering pages:', error);
                statusElement.textContent = `Rendering error: ${error.message}`;
                throw error;
            }
        }

        async function printInNewWindow(printContainer) {
            return new Promise((resolve, reject) => {
                try {
                    // Create new window for printing
                    const printWindow = window.open('', '_blank', 'width=800,height=600');

                    if (!printWindow) {
                        throw new Error('Popup blocked or failed to open print window');
                    }

                    // Create HTML content for print window
                    const printHTML = `
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>PDF Print</title>
                            <style>
                                body {
                                    margin: 0;
                                    padding: 0;
                                    font-family: Arial, sans-serif;
                                }
                                .pdfjs-print-page {
                                    page-break-after: always;
                                    page-break-inside: avoid;
                                    margin: 0;
                                    padding: 0;
                                    width: 100%;
                                    height: auto;
                                    display: block;
                                }
                                .pdfjs-print-page:last-child {
                                    page-break-after: avoid;
                                }
                                @page {
                                    margin: 0.5in;
                                    size: auto;
                                }
                                @media print {
                                    body { margin: 0; padding: 0; }
                                }
                            </style>
                        </head>
                        <body>
                            ${printContainer.innerHTML}
                        </body>
                        </html>
                    `;

                    // Write content to new window
                    printWindow.document.write(printHTML);
                    printWindow.document.close();

                    // Wait for content to load, then print
                    printWindow.onload = function() {
                        setTimeout(() => {
                            try {
                                printWindow.focus();
                                printWindow.print();
                                console.log('Print dialog opened in new window');
                                resolve();

                                // Optional: Close window after printing
                                // printWindow.onafterprint = function() {
                                //     printWindow.close();
                                // };
                            } catch (e) {
                                reject(e);
                            }
                        }, 500);
                    };

                    printWindow.onerror = function(error) {
                        reject(new Error('Print window error: ' + error));
                    };

                } catch (error) {
                    reject(error);
                }
            });
        }

        async function printInCurrentWindow(printContainer) {
            return new Promise((resolve, reject) => {
                try {
                    // Show the print container temporarily
                    printContainer.style.display = 'block';
                    printContainer.style.position = 'fixed';
                    printContainer.style.top = '0';
                    printContainer.style.left = '0';
                    printContainer.style.width = '100%';
                    printContainer.style.height = '100%';
                    printContainer.style.overflow = 'auto';
                    printContainer.style.backgroundColor = 'white';
                    printContainer.style.zIndex = '10000';

                    // Focus the print container
                    printContainer.focus();

                    // Small delay to ensure rendering is complete, then print
                    setTimeout(() => {
                        try {
                            window.print();
                            console.log('Print dialog opened in current window');
                            resolve();
                        } catch (e) {
                            reject(e);
                        }

                        // Hide the print container after printing
                        setTimeout(() => {
                            printContainer.style.display = 'none';
                        }, 1000);
                    }, 500);

                } catch (error) {
                    reject(error);
                }
            });
        }

        // Alternative method: Open PDF in new window and print
        function printPDFNewWindow() {
            const printWindow = window.open(PDF_FILE_PATH, '_blank');

            printWindow.onload = function() {
                printWindow.print();
                // Optionally close the window after printing
                // printWindow.onafterprint = function() {
                //     printWindow.close();
                // };
            };
        }
        
        // You can uncomment the line below to use the new window method instead
        // document.querySelector('.print-button').onclick = printPDFNewWindow;
        
        // Handle object load events
        document.getElementById('pdf-object').onload = function() {
            console.log('PDF object loaded successfully');
        };

        document.getElementById('pdf-object').onerror = function() {
            console.log('Error loading PDF object');
        };

        // Debug function to check browser capabilities
        function checkBrowserCapabilities() {
            console.log('=== Browser Capabilities Debug ===');

            // Device detection
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isAndroid = /Android/.test(navigator.userAgent);
            const isPWA = window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone;

            console.log('Device info:', {
                isMobile,
                isIOS,
                isAndroid,
                isPWA,
                userAgent: navigator.userAgent,
                platform: navigator.platform
            });

            // Print capabilities
            console.log('Print support:', {
                windowPrint: typeof window.print === 'function',
                mediaQueryPrint: window.matchMedia('print').media === 'print'
            });

            // Sharing capabilities
            console.log('Sharing support:', {
                navigatorShare: !!navigator.share,
                navigatorCanShare: !!navigator.canShare,
                clipboard: !!navigator.clipboard
            });

            if (navigator.canShare) {
                try {
                    console.log('Share capabilities:', {
                        canShareURL: navigator.canShare({ url: 'https://example.com' }),
                        canShareFiles: navigator.canShare({ files: [new File([''], 'test.pdf', { type: 'application/pdf' })] })
                    });
                } catch (e) {
                    console.log('Error checking share capabilities:', e);
                }
            }

            // PDF object capabilities
            const pdfObject = document.getElementById('pdf-object');
            if (pdfObject) {
                console.log('PDF Object info:', {
                    hasContentWindow: !!pdfObject.contentWindow,
                    hasContentDocument: !!pdfObject.contentDocument,
                    hasPrintMethod: typeof pdfObject.print === 'function',
                    objectData: pdfObject.data,
                    objectType: pdfObject.type
                });
            }

            console.log('Current URL:', window.location.href);
            console.log('PDF file path:', PDF_FILE_PATH);
        }

        // Call debug function on page load
        window.addEventListener('load', checkBrowserCapabilities);
    </script>
</body>
</html>
