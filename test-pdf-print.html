<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Print Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: #f0f0f0;
            padding: 15px 20px;
            border-bottom: 1px solid #ccc;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
        }
        
        .print-button, .share-button, .share-url-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-left: 10px;
        }

        .print-button:hover, .share-button:hover, .share-url-button:hover {
            background-color: #0056b3;
        }

        .print-button:active, .share-button:active, .share-url-button:active {
            background-color: #004085;
        }

        .share-button {
            background-color: #28a745;
        }

        .share-button:hover {
            background-color: #218838;
        }

        .share-button:active {
            background-color: #1e7e34;
        }

        .share-url-button {
            background-color: #17a2b8;
        }

        .share-url-button:hover {
            background-color: #138496;
        }

        .share-url-button:active {
            background-color: #117a8b;
        }

        .button-group {
            display: flex;
            gap: 10px;
        }
        
        .pdf-container {
            flex: 1;
            padding: 0;
            margin: 0;
        }

        #pdf-object {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        /* Print styles */
        @media print {
            .header {
                display: none;
            }
            
            body {
                margin: 0;
                padding: 0;
            }

            .pdf-container {
                height: 100vh;
            }

            #pdf-object {
                height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>PDF Print Test</h1>
        <div class="button-group">
            <button class="print-button" onclick="printPDF()">🖨️ Print PDF</button>
            <button class="share-button" onclick="sharePDF()">📤 Share PDF</button>
            <button class="share-url-button" onclick="shareURL()">🔗 Share URL</button>
        </div>
    </div>
    
    <div class="pdf-container">
        <object id="pdf-object" data="" type="application/pdf" title="PDF Document">
            <p>Your browser does not support PDF viewing. Please <a href="" id="pdf-download-link">download the PDF</a> to view it.</p>
        </object>
    </div>

    <script>
        // PDF file configuration - change this constant to use a different PDF
        const PDF_FILE_PATH = 'data/veolia-protocol.pdf';

        // Set the PDF source when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            const pdfObject = document.getElementById('pdf-object');
            const downloadLink = document.getElementById('pdf-download-link');

            pdfObject.data = PDF_FILE_PATH;
            downloadLink.href = PDF_FILE_PATH;
        });

        function printPDF() {
            try {
                // Method 1: Try to print the object content directly
                const pdfObject = document.getElementById('pdf-object');

                // Check if object has contentWindow (PDF plugin loaded)
                if (pdfObject.contentWindow) {
                    pdfObject.contentWindow.focus();
                    pdfObject.contentWindow.print();
                    console.log('PDF printed via object contentWindow');
                    return;
                }

                // Method 2: Try to access the PDF document directly
                if (pdfObject.contentDocument) {
                    pdfObject.contentDocument.defaultView.print();
                    console.log('PDF printed via object contentDocument');
                    return;
                }

                // Method 3: Try to trigger print on the object itself
                if (typeof pdfObject.print === 'function') {
                    pdfObject.print();
                    console.log('PDF printed via object.print()');
                    return;
                }

                // Method 4: Fallback - print the entire page (object will be visible due to print styles)
                console.log('Using fallback method - printing entire page');
                window.print();

            } catch (error) {
                console.log('Direct object printing failed, using fallback method:', error);
                // Final fallback - print the whole page
                window.print();
            }
        }

        async function sharePDF() {
            try {
                // Construct the full PDF URL
                const baseUrl = window.location.href.replace(/\/[^\/]*$/, '');
                const pdfUrl = baseUrl + '/' + PDF_FILE_PATH;

                console.log('Attempting to share PDF from:', pdfUrl);

                // Check if Navigator.share is supported
                if (!navigator.share) {
                    console.log('Navigator.share not supported, using fallback');
                    await fallbackShare(pdfUrl);
                    return;
                }

                // First try sharing just the URL (more reliable on iOS)
                try {
                    await navigator.share({
                        title: 'PDF Document',
                        text: 'Check out this PDF document',
                        url: pdfUrl
                    });
                    console.log('PDF URL shared successfully');
                    return;
                } catch (urlShareError) {
                    console.log('URL sharing failed, trying file sharing:', urlShareError);
                }

                // If URL sharing fails, try file sharing
                try {
                    // Check if file sharing is supported
                    if (!navigator.canShare || !navigator.canShare({ files: [new File([''], 'test.pdf', { type: 'application/pdf' })] })) {
                        throw new Error('File sharing not supported');
                    }

                    // Fetch the PDF file as a blob
                    const response = await fetch(pdfUrl);

                    if (!response.ok) {
                        throw new Error(`Failed to fetch PDF file: ${response.status} ${response.statusText}`);
                    }

                    const blob = await response.blob();
                    // Extract filename from path or use default
                    const filename = PDF_FILE_PATH.split('/').pop() || 'document.pdf';
                    const file = new File([blob], filename, { type: 'application/pdf' });

                    // Use Navigator.share to share the PDF file
                    await navigator.share({
                        title: 'PDF Document',
                        text: 'Check out this PDF document',
                        files: [file]
                    });

                    console.log('PDF file shared successfully');

                } catch (fileShareError) {
                    console.log('File sharing failed:', fileShareError);
                    throw fileShareError;
                }

            } catch (error) {
                console.error('Error sharing PDF:', error);

                // Fallback to URL sharing methods
                const baseUrl = window.location.href.replace(/\/[^\/]*$/, '');
                const pdfUrl = baseUrl + '/' + PDF_FILE_PATH;
                await fallbackShare(pdfUrl);
            }
        }

        async function shareURL() {
            try {
                // Construct the full PDF URL
                const baseUrl = window.location.href.replace(/\/[^\/]*$/, '');
                const pdfUrl = baseUrl + '/' + PDF_FILE_PATH;

                console.log('Attempting to share PDF URL:', pdfUrl);

                // Check if Navigator.share is supported
                if (navigator.share) {
                    try {
                        await navigator.share({
                            title: 'PDF Document',
                            text: 'Check out this PDF document',
                            url: pdfUrl
                        });
                        console.log('PDF URL shared successfully via Navigator.share');
                        return;
                    } catch (shareError) {
                        console.log('Navigator.share failed:', shareError);
                        // Fall through to clipboard fallback
                    }
                }

                // Fallback to clipboard
                await fallbackShare(pdfUrl);

            } catch (error) {
                console.error('Error sharing PDF URL:', error);

                // Final fallback
                const baseUrl = window.location.href.replace(/\/[^\/]*$/, '');
                const pdfUrl = baseUrl + '/' + PDF_FILE_PATH;
                alert('Please copy this PDF URL to share:\n' + pdfUrl);
            }
        }

        async function fallbackShare(pdfUrl) {
            try {
                // Try to copy URL to clipboard
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    await navigator.clipboard.writeText(pdfUrl);
                    alert('PDF URL copied to clipboard:\n' + pdfUrl);
                } else {
                    // Final fallback - show URL in alert for manual copying
                    alert('Please copy this PDF URL to share:\n' + pdfUrl);
                }
            } catch (clipboardError) {
                console.error('Clipboard error:', clipboardError);
                // Final fallback - show URL in alert
                alert('Please copy this PDF URL to share:\n' + pdfUrl);
            }
        }
        
        // Alternative method: Open PDF in new window and print
        function printPDFNewWindow() {
            const printWindow = window.open(PDF_FILE_PATH, '_blank');

            printWindow.onload = function() {
                printWindow.print();
                // Optionally close the window after printing
                // printWindow.onafterprint = function() {
                //     printWindow.close();
                // };
            };
        }
        
        // You can uncomment the line below to use the new window method instead
        // document.querySelector('.print-button').onclick = printPDFNewWindow;
        
        // Handle object load events
        document.getElementById('pdf-object').onload = function() {
            console.log('PDF object loaded successfully');
        };

        document.getElementById('pdf-object').onerror = function() {
            console.log('Error loading PDF object');
        };

        // Debug function to check sharing capabilities
        function checkSharingCapabilities() {
            console.log('=== Sharing Capabilities Debug ===');
            console.log('navigator.share supported:', !!navigator.share);
            console.log('navigator.canShare supported:', !!navigator.canShare);
            console.log('navigator.clipboard supported:', !!navigator.clipboard);
            console.log('User agent:', navigator.userAgent);
            console.log('Current URL:', window.location.href);

            if (navigator.canShare) {
                console.log('Can share URL:', navigator.canShare({ url: 'https://example.com' }));
                console.log('Can share files:', navigator.canShare({ files: [new File([''], 'test.pdf', { type: 'application/pdf' })] }));
            }
        }

        // Call debug function on page load
        window.addEventListener('load', checkSharingCapabilities);
    </script>
</body>
</html>
